package com.dell.it.hip.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;

import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

public class PropertySheetFetcherTest {

    @Test
    public void testCombineAllJsonNodeValues() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        Map<String, Object> combinedProps = new LinkedHashMap<>();
        
        // Sample JSON with multiple property sources (similar to the main method)
        String json = "{\"name\":\"test-sheet\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.password\":\"password1\",\"kafka.producer.sasl.jaas.config.username\":\"user1\",\"kafka.producer.ssl.truststore.password\":\"trustpass1\"}},{\"name\":\"vault:shared-kafka-consumer\",\"source\":{\"kafka.consumer.sasl.jaas.config.password\":\"password2\",\"kafka.consumer.sasl.jaas.config.username\":\"user2\",\"kafka.consumer.ssl.truststore.password\":\"trustpass2\"}},{\"name\":\"vault:shared-ibmmq-producer\",\"source\":{\"ibm.mq.producer.password\":\"mqpassword\",\"ibm.mq.producer.user\":\"mquser\"}}]}";

        JsonNode root = objectMapper.readTree(json);
        JsonNode sources = root.get("propertySources");
       
        if (sources != null && sources.isArray()) {
            for (JsonNode src : sources) {
                JsonNode sourceProps = src.get("source");
                if (sourceProps != null) {
                    // Iterate through all fields in this source and add them to the combined properties
                    Iterator<Map.Entry<String, JsonNode>> fields = sourceProps.fields();
                    while (fields.hasNext()) {
                        Map.Entry<String, JsonNode> entry = fields.next();
                        // Convert JsonNode to appropriate Object type for the combined map
                        Object value;
                        if (entry.getValue().isTextual()) {
                            value = entry.getValue().asText();
                        } else if (entry.getValue().isNumber()) {
                            value = entry.getValue().numberValue();
                        } else if (entry.getValue().isBoolean()) {
                            value = entry.getValue().asBoolean();
                        } else {
                            // For complex objects, keep as JsonNode or convert to string
                            value = entry.getValue().toString();
                        }
                        combinedProps.put(entry.getKey(), value);
                    }
                }
            }
        }
       
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", combinedProps);
        
        // Verify that all properties from all sources are combined
        @SuppressWarnings("unchecked")
        Map<String, Object> mysheet = (Map<String, Object>) result.get("mysheet");
        
        // Verify properties from first source (kafka-producer)
        assertEquals("password1", mysheet.get("kafka.producer.sasl.jaas.config.password"));
        assertEquals("user1", mysheet.get("kafka.producer.sasl.jaas.config.username"));
        assertEquals("trustpass1", mysheet.get("kafka.producer.ssl.truststore.password"));
        
        // Verify properties from second source (kafka-consumer)
        assertEquals("password2", mysheet.get("kafka.consumer.sasl.jaas.config.password"));
        assertEquals("user2", mysheet.get("kafka.consumer.sasl.jaas.config.username"));
        assertEquals("trustpass2", mysheet.get("kafka.consumer.ssl.truststore.password"));
        
        // Verify properties from third source (ibmmq-producer)
        assertEquals("mqpassword", mysheet.get("ibm.mq.producer.password"));
        assertEquals("mquser", mysheet.get("ibm.mq.producer.user"));
        
        // Verify total number of properties (should be 8 total)
        assertEquals(8, mysheet.size());
        
        System.out.println("Combined properties: " + result.toString());
        System.out.println("Test passed! All JSON node values are properly combined.");
    }
    
    @Test
    public void testOriginalBehaviorWouldOnlyKeepLastSource() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode sourceProps = null; // This simulates the original buggy behavior
        
        String json = "{\"name\":\"test-sheet\",\"profiles\":[\"cloud\"],\"label\":null,\"version\":null,\"state\":null,\"propertySources\":[{\"name\":\"vault:shared-kafka-producer\",\"source\":{\"kafka.producer.sasl.jaas.config.password\":\"password1\",\"kafka.producer.sasl.jaas.config.username\":\"user1\"}},{\"name\":\"vault:shared-kafka-consumer\",\"source\":{\"kafka.consumer.sasl.jaas.config.password\":\"password2\",\"kafka.consumer.sasl.jaas.config.username\":\"user2\"}},{\"name\":\"vault:shared-ibmmq-producer\",\"source\":{\"ibm.mq.producer.password\":\"mqpassword\",\"ibm.mq.producer.user\":\"mquser\"}}]}";

        JsonNode root = objectMapper.readTree(json);
        JsonNode sources = root.get("propertySources");
       
        if (sources != null && sources.isArray()) {
            for (JsonNode src : sources) {
                sourceProps = src.get("source"); // This overwrites on each iteration - the bug!
            }
        }
       
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("mysheet", sourceProps);
        
        // With the original buggy behavior, only the last source would be present
        JsonNode mysheet = (JsonNode) result.get("mysheet");
        
        // Only properties from the last source (ibmmq-producer) would be present
        assertTrue(mysheet.has("ibm.mq.producer.password"));
        assertTrue(mysheet.has("ibm.mq.producer.user"));
        
        // Properties from earlier sources would be missing
        assertFalse(mysheet.has("kafka.producer.sasl.jaas.config.password"));
        assertFalse(mysheet.has("kafka.consumer.sasl.jaas.config.password"));
        
        System.out.println("Original buggy behavior result: " + result.toString());
        System.out.println("This demonstrates the original issue - only last source is kept!");
    }
}
