package com.dell.it.hip.config.Handlers;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class DynamicKafkaHandlerConfig extends HandlerConfig{
    // Unique ID for this handler config (used as propertyRef)
    private String configRef;

    // Core Kafka settings
    @JsonProperty("kafka.producer.bootstrapServers")
    private String bootstrapServers;
    
    @JsonProperty("kafka.producer.topic")
    private String topic;
    
    private String clientId;
    
    @JsonProperty("kafka.producer.username")
	private String username;
    
	@JsonProperty("kafka.producer.password")
	private String password;

    // Security
	@JsonProperty("kafka.producer.securityProtocol")
    private String securityProtocol;         // PLAINTEXT, SSL, SASL_PLAINTEXT, SASL_SSL
	
	@JsonProperty("kafka.producer.saslMechanism")
    private String saslMechanism;            // PLAIN, SCRAM-SHA-256, SCRAM-SHA-512, GSSAPI, etc.
	
	@JsonProperty("kafka.producer.sasljaasconfig")
    private String sasljaasconfig;           // e.g., "org.apache.kafka.common.security.plain.PlainLoginModule required username='...' password='...';"
   
    @JsonProperty("kafka.producer.sslTruststoreLocation")
    private String sslTruststoreLocation;
    
    @JsonProperty("kafka.producer.sslTruststorePassword")
    private String sslTruststorePassword;
    
    private String sslKeystoreLocation;
    private String sslKeystorePassword;
    private String sslKeyPassword;
    
    @JsonProperty("kafka.producer.ssltruststoretype")
    private String ssltruststoretype;
    
    @JsonProperty("kafka.producer.protocols")
    private String protocols;

    // Optimization/tuning
    private Integer acks;                    // 0, 1, all
    private Integer batchSize;               // bytes, e.g., 16384
    private Integer lingerMs;                // ms, e.g., 5
    private Integer bufferMemory;            // bytes, e.g., 33554432
    private Integer retries;
    private Integer maxInFlightRequestsPerConnection;
    private Integer deliveryTimeoutMs;
    private Integer requestTimeoutMs;
    private Boolean enableIdempotence;
    private Integer compressionType;         // NONE, GZIP, SNAPPY, LZ4, ZSTD (string or int)
    private Boolean gzipEnabled;             // Explicit flag for your app logic (separate from Kafka compression.type)

    // Application-level
    private Boolean archiveEnabled;          // For pre-send archiving
    private Map<String, Object> parameters;  // Any additional/custom configs (header filters, etc.)

    // Getters and setters for all fields
    // ...
    // (Omitted for brevity; use your IDE to generate)
}